steps:
  # 构建Docker镜像
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/fillify-backend', '.']

  # 推送镜像到Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/fillify-backend']

  # 部署到Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'fillify-backend'
      - '--image'
      - 'gcr.io/$PROJECT_ID/fillify-backend'
      - '--platform'
      - 'managed'
      - '--region'
      - 'asia-east1'  # 选择亚洲区域以减少延迟
      - '--allow-unauthenticated'  # 允许公开访问
      - '--memory'
      - '256Mi'  # 降低内存使用
      - '--timeout'
      - '120'    # 设置超时时间为2分钟
      - '--cpu'
      - '0.5'    # 使用更少的CPU
      - '--concurrency'
      - '80'     # 限制并发请求数
      - '--max-instances'
      - '1'      # 限制最大实例数

images:
  - 'gcr.io/$PROJECT_ID/fillify-backend' 