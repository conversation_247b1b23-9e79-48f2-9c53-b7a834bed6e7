import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { json, raw } from 'express';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // 设置全局路由前缀
  app.setGlobalPrefix('api');
  
  // 为 Stripe webhook 添加 raw body 解析
  app.use('/api/payment/webhook', raw({ type: 'application/json' }));
  
  // 设置请求体解析器的超时时间
  app.use(json({ limit: '10mb', strict: true }));
  
  // 全局启用验证管道
  app.useGlobalPipes(new ValidationPipe());
  
  // 配置CORS
  app.enableCors({
    origin: true, // 允许所有合法的跨域请求
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    allowedHeaders: ['Content-Type', 'Accept', 'Authorization', 'Origin', 'X-Requested-With'],
    credentials: true,
    maxAge: 86400, // 24 hours
  });

  // 使用Cloud Run提供的端口或默认8080
  await app.listen(process.env.PORT || 8080);
}
bootstrap(); 