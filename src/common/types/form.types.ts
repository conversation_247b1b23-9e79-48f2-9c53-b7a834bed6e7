import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';

export enum FormMode {
  BUG_REPORT = 'bugReport',
  GENERAL = 'general',
  EMAIL = 'email'
}

export interface EnvironmentInfo {
  os?: string;
  browser?: string;
  language?: string;
}

export class FormRequest {
  @IsString()
  description: string;

  @IsArray()
  formFields: string[];

  @IsEnum(FormMode)
  mode: FormMode;

  @IsString()
  @IsOptional()
  apiKey?: string;

  @IsString()
  @IsOptional()
  projectId?: string;

  @IsOptional()
  project?: ProjectInfo;

  @IsString()
  @IsOptional()
  provider?: string;

  @IsString()
  @IsOptional()
  model?: string;

  @IsOptional()
  useCustomApi?: boolean;

  @IsString()
  @IsOptional()
  userId?: string;

  @IsOptional()
  environmentInfo?: EnvironmentInfo;

  @IsString()
  @IsOptional()
  language?: string;
}

export interface ProjectInfo {
  id?: string;
  name: string;
  description?: string;
  environment?: string;
  info?: string;
  template?: string;
}

export interface FormResponse {
  success: boolean;
  data: any;
}

export interface AIProviderConfig {
  apiKey: string;
  model?: string;
}

export interface AIResponse {
  content: string;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface OpenAIResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  error?: {
    message: string;
  };
}

export interface MoonshotResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  error?: {
    message: string;
  };
}

export interface ClaudeResponse {
  id: string;
  type: string;
  role: string;
  content: Array<{
    type: string;
    text: string;
  }>;
  model: string;
  stop_reason: string;
  stop_sequence: string | null;
  usage: {
    input_tokens: number;
    output_tokens: number;
  };
  error?: {
    type: string;
    message: string;
  };
}

export interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
      role?: string;
    };
    finishReason?: string;
    index?: number;
    safetyRatings?: Array<{
      category: string;
      probability: string;
    }>;
  }>;
  promptFeedback?: {
    blockReason?: string;
    safetyRatings?: Array<{
      category: string;
      probability: string;
    }>;
    tokenCount?: {
      totalTokens: number;
      promptTokens: number;
    };
  };
  usageMetadata?: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
  error?: {
    message: string;
    code?: number;
    status?: string;
    details?: any;
  };
}

export interface OpenRouterResponse {
  id: string;
  choices: Array<{
    finish_reason: string | null;
    native_finish_reason: string | null;
    message: {
      content: string | null;
      role: string;
      tool_calls?: Array<any>;
    };
    error?: {
      code: number;
      message: string;
      metadata?: Record<string, unknown>;
    };
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  model: string;
  object: 'chat.completion' | 'chat.completion.chunk';
  system_fingerprint?: string;
}