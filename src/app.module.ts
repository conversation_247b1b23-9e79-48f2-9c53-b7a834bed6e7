import { Module } from '@nestjs/common';
import { FormModule } from './modules/form/form.module';
import { AuthModule } from './modules/auth/auth.module';
import { ConfigModule } from '@nestjs/config';
import { PaymentModule } from './modules/payment/payment.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    FormModule,
    AuthModule,
    PaymentModule,
  ],
})
export class AppModule {} 