import { Controller, Post, Body } from '@nestjs/common';
import { UserService } from '../services/user.service';
import { GetUserDto } from '../dtos/auth.dto';

@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post('get-user')
  async getUser(@Body() getUserDto: GetUserDto) {
    return this.userService.getUser(getUserDto);
  }

  @Post('credit-stats')
  async getCreditStats(@Body() body: { userId: string }) {
    return this.userService.getCreditStats(body.userId);
  }

  @Post('credit-transactions')
  async getCreditTransactions(@Body() body: { userId: string }) {
    return this.userService.getCreditTransactions(body.userId);
  }
} 