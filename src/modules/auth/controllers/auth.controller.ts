import { Controller, Post, Body } from '@nestjs/common';
import { AuthService } from '../services/auth.service';
import { GoogleLoginDto } from '../dtos/auth.dto';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('google-login')
  async googleLogin(@Body() googleLoginDto: GoogleLoginDto) {
    return this.authService.googleLogin(googleLoginDto);
  }

  @Post('logout')
  async logout() {
    return this.authService.logout();
  }
} 