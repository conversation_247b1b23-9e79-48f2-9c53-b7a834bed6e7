export class GoogleLoginDto {
  credential: string;
}

export class UserResponseDto {
  id: string;
  google_id: string;
  full_name: string;
  given_name: string;
  family_name: string;
  picture_url: string;
  email: string;
  credits?: number;
  subscription_plan?: string; // 'free' | 'pro' | 'max'
  subscription_status?: string; // 'active' | 'canceled' | 'past_due'
  stripe_customer_id?: string;
  subscription_id?: string;
  updated_at: string;
}

export class GetUserDto {
  userId: string;
} 