import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { createClient } from '@supabase/supabase-js';
import { OAuth2Client } from 'google-auth-library';
import { GoogleLoginDto, UserResponseDto } from '../dtos/auth.dto';

@Injectable()
export class AuthService {
  private supabase;
  private googleClient;

  constructor(
    private configService: ConfigService,
    private jwtService: JwtService
  ) {
    // 初始化 Supabase 客户端
    this.supabase = createClient(
      this.configService.get<string>('SUPABASE_URL'),
      this.configService.get<string>('SUPABASE_SERVICE_ROLE_KEY')
    );

    // 初始化 Google OAuth 客户端
    this.googleClient = new OAuth2Client(
      this.configService.get<string>('GOOGLE_CLIENT_ID')
    );
  }

  async googleLogin(googleLoginDto: GoogleLoginDto): Promise<{ success: boolean; user?: UserResponseDto; token?: string; error?: string }> {
    try {
      // 验证 Google token
      const ticket = await this.googleClient.verifyIdToken({
        idToken: googleLoginDto.credential,
        audience: this.configService.get<string>('GOOGLE_CLIENT_ID'),
      });

      const payload = ticket.getPayload();
      if (!payload) {
        throw new Error('Invalid payload');
      }

      // 首先检查用户是否已存在
      const { data: existingUser } = await this.supabase
        .from('users')
        .select('*')
        .eq('google_id', payload.sub)
        .single();

      const isNewUser = !existingUser;
      const initialCredits = 10;

      // 在 Supabase 中存储用户信息
      const { data, error } = await this.supabase
        .from('users')
        .upsert({
          google_id: payload.sub,
          full_name: payload.name,
          given_name: payload.given_name,
          family_name: payload.family_name,
          picture_url: payload.picture,
          email: payload.email,
          // 如果是新用户，则设置初始credits为10
          ...(isNewUser ? { credits: initialCredits } : {}),
          updated_at: new Date().toISOString()
        }, { 
          onConflict: 'google_id'
        })
        .select();

      if (error) throw error;
      if (!data || data.length === 0) {
        return { success: false, error: 'No user data returned' };
      }

      // 如果是新用户，添加初始 credits 交易记录
      if (isNewUser) {
        const { error: transactionError } = await this.supabase
          .from('credit_transactions')
          .insert({
            user_id: data[0].id,
            amount: initialCredits,
            type: 'initial',
            description: 'Welcome bonus credits'
          });

        if (transactionError) {
          console.error('Error creating initial credit transaction:', transactionError);
        }
      }

      // 生成 JWT token
      const token = this.jwtService.sign({
        sub: data[0].id,
        email: data[0].email
      });

      return { 
        success: true, 
        user: data[0] as UserResponseDto,
        token 
      };
    } catch (error) {
      console.error('Google login error:', error);
      return { success: false, error: 'Failed to authenticate user' };
    }
  }

  async logout(): Promise<{ success: boolean; message?: string; error?: string }> {
    try {
      const { error } = await this.supabase.auth.signOut();

      if (error) {
        throw error;
      }

      return {
        success: true,
        message: 'User logged out successfully'
      };
    } catch (error) {
      console.error('Logout error:', error);
      return {
        success: false,
        error: 'Failed to log out user'
      };
    }
  }
} 