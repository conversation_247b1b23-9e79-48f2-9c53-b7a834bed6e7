import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { createClient } from '@supabase/supabase-js';
import { GetUserDto, UserResponseDto } from '../dtos/auth.dto';

@Injectable()
export class UserService {
  private supabase;

  constructor(
    private configService: ConfigService,
    private jwtService: JwtService
  ) {
    // 初始化 Supabase 客户端
    this.supabase = createClient(
      this.configService.get<string>('SUPABASE_URL'),
      this.configService.get<string>('SUPABASE_SERVICE_ROLE_KEY')
    );
  }

  async getUser(getUserDto: GetUserDto): Promise<{ success: boolean; user?: UserResponseDto; token?: string; error?: string }> {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select('*')
        .eq('id', getUserDto.userId)
        .single();

      if (error) throw error;
      if (!data) {
        return { success: false, error: 'User not found' };
      }

      // 生成新的 JWT token
      const token = this.jwtService.sign({
        sub: data.id,
        email: data.email
      });

      return { 
        success: true, 
        user: data as UserResponseDto,
        token  // 返回新生成的 token
      };
    } catch (error) {
      console.error('Error fetching user data:', error);
      return { success: false, error: 'Failed to fetch user data' };
    }
  }

  async getCreditStats(userId: string): Promise<{ success: boolean; stats?: any; error?: string }> {
    try {
      const { data, error } = await this.supabase
        .from('user_credit_stats')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) throw error;

      return { 
        success: true, 
        stats: data || {
          total_credits_received: 0,
          total_credits_used: 0,
          current_balance: 0
        }
      };
    } catch (error) {
      console.error('Error fetching credit stats:', error);
      return { success: false, error: 'Failed to fetch credit stats' };
    }
  }

  async getCreditTransactions(userId: string): Promise<{ success: boolean; transactions?: any[]; error?: string }> {
    try {
      const { data, error } = await this.supabase
        .from('credit_transactions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return { 
        success: true, 
        transactions: data || []
      };
    } catch (error) {
      console.error('Error fetching credit transactions:', error);
      return { success: false, error: 'Failed to fetch credit transactions' };
    }
  }
} 