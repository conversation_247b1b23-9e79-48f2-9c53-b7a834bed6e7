import { Injectable, UnauthorizedException, BadRequestException } from '@nestjs/common';
import Stripe from 'stripe';
import { ConfigService } from '@nestjs/config';
import { SupabaseClient, createClient } from '@supabase/supabase-js';

interface PlanDetails {
  credits: number;
  name: string;
  price: number;
}

@Injectable()
export class PaymentService {
  private readonly stripe: Stripe;
  private readonly supabase: SupabaseClient;
  private readonly planDetails: { [key: string]: PlanDetails } = {
    'price_1QdArJGdTEIio1OhJbJd9h0Y': { credits: 1000, name: 'pro', price: 3.99 },
    'price_1QdAsYGdTEIio1Ohwby9pPgq': { credits: 3000, name: 'max', price: 5.99 }
  };

  constructor(private configService: ConfigService) {
    this.stripe = new Stripe(this.configService.get<string>('STRIPE_SECRET_KEY'), {
      apiVersion: '2024-12-18.acacia',
    });
    
    this.supabase = createClient(
      this.configService.get<string>('SUPABASE_URL'),
      this.configService.get<string>('SUPABASE_SERVICE_ROLE_KEY')
    );

    console.log('Initialized PaymentService with planDetails:', this.planDetails);
  }

  async createCheckoutSession(priceId: string, userId: string): Promise<{ url: string; sessionId: string }> {
    try {
      // 验证用户是否存在
      const { data: user, error: userError } = await this.supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (userError || !user) {
        throw new UnauthorizedException('User not found');
      }

      // 检查用户当前的订阅状态
      const planDetails = this.planDetails[priceId];
      if (!planDetails) {
        throw new BadRequestException('Invalid price ID');
      }

      // 如果用户已经订阅了相同的计划且订阅状态为 active，则不允许重复订阅
      if (
        user.subscription_plan === planDetails.name &&
        user.subscription_status === 'active'
      ) {
        throw new BadRequestException('You already have an active subscription to this plan');
      }

      // 如果用户当前是 Pro 计划，且要升级到 Max 计划，使用 Billing Portal
      if (
        user.subscription_plan === 'pro' &&
        user.subscription_status === 'active' &&
        planDetails.name === 'max'
      ) {
        const session = await this.stripe.billingPortal.sessions.create({
          customer: user.stripe_customer_id,
          return_url: `${this.configService.get<string>('FRONTEND_URL')}/dashboard`,
          flow_data: {
            type: 'subscription_update',
            subscription_update: {
              subscription: user.subscription_id
            }
          }
        });

        return { url: session.url, sessionId: session.id };
      }

      // 获取或创建 Stripe 客户
      let stripeCustomerId = user.stripe_customer_id;
      if (!stripeCustomerId) {
        const customer = await this.stripe.customers.create({
          email: user.email,
          metadata: {
            userId: user.id
          }
        });
        stripeCustomerId = customer.id;
        
        // 更新用户的 stripe_customer_id
        await this.supabase
          .from('users')
          .update({ stripe_customer_id: stripeCustomerId })
          .eq('id', userId);
      }

      // 创建新订阅的 checkout session
      const session = await this.stripe.checkout.sessions.create({
        customer: stripeCustomerId,
        payment_method_types: ['card'],
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        mode: 'subscription',
        success_url: `${this.configService.get<string>('FRONTEND_URL')}/dashboard?session_id={CHECKOUT_SESSION_ID}&status=success`,
        cancel_url: `${this.configService.get<string>('FRONTEND_URL')}/dashboard?status=canceled`,
        metadata: {
          userId: userId,
          priceId: priceId
        }
      });

      return { url: session.url, sessionId: session.id };
    } catch (error) {
      console.error('Error creating checkout session:', error);
      throw error;
    }
  }

  async checkSessionStatus(sessionId: string): Promise<{ success: boolean; subscription?: any }> {
    try {
      const session = await this.stripe.checkout.sessions.retrieve(sessionId);
      console.log('Session status:', session.payment_status, session.status);
      
      if (session.payment_status === 'paid' && session.status === 'complete') {
        const subscription = await this.stripe.subscriptions.retrieve(session.subscription as string);
        const userId = session.metadata.userId;
        const priceId = session.metadata.priceId;
        const planDetails = this.planDetails[priceId];
        console.log('Plan details in checkSessionStatus:', planDetails);

        if (userId && planDetails) {
          console.log('Updating user subscription in checkSessionStatus. User ID:', userId);
          
          // 只更新订阅状态，积分将通过 webhook 更新
          const { data, error } = await this.supabase
            .from('users')
            .update({
              subscription_plan: planDetails.name,
              subscription_status: subscription.status,
              subscription_id: subscription.id
            })
            .eq('id', userId);

          if (error) {
            console.error('Error updating user in checkSessionStatus:', error);
          } else {
            console.log('Successfully updated user subscription status in checkSessionStatus');
          }

          return { success: true, subscription };
        }
      }
      
      return { success: false };
    } catch (error) {
      console.error('Error checking session status:', error);
      return { success: false };
    }
  }

  async handleWebhook(
    signature: string,
    rawBody: Buffer
  ) {
    try {
      const event = this.stripe.webhooks.constructEvent(
        rawBody,
        signature,
        this.configService.get('STRIPE_WEBHOOK_SECRET')
      );

      console.log('Received webhook event:', event.type);

      switch (event.type) {
        case 'customer.subscription.created':
        case 'customer.subscription.updated':
        case 'invoice.payment_succeeded':
          await this.handleSubscriptionUpdate(event.data.object, event.type);
          break;
        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object);
          break;
      }

      return { received: true };
    } catch (error) {
      console.error('Error handling webhook:', error);
      throw new Error('Webhook handling failed');
    }
  }

  private async handleSubscriptionUpdate(subscription: any, eventType: string) {
    try {
      // 如果事件是发票付款成功，需要获取订阅ID
      const subscriptionId = subscription.subscription || subscription.id;
      if (!subscriptionId) {
        console.error('No subscription ID found in webhook data');
        return;
      }

      // 获取完整的订阅信息
      const fullSubscription = await this.stripe.subscriptions.retrieve(subscriptionId);
      
      // 首先尝试通过 stripe_customer_id 查找用户
      const customerId = fullSubscription.customer;
      let { data: users } = await this.supabase
        .from('users')
        .select('*')
        .eq('stripe_customer_id', customerId)
        .limit(1);

      // 如果找不到用户，尝试通过 metadata.userId 查找
      if (!users || users.length === 0) {
        console.log('User not found by stripe_customer_id, trying metadata.userId');
        const customer = await this.stripe.customers.retrieve(customerId as string);
        if (customer && 'metadata' in customer && customer.metadata && customer.metadata.userId) {
          const { data: usersById } = await this.supabase
            .from('users')
            .select('*')
            .eq('id', customer.metadata.userId)
            .limit(1);
          
          if (usersById && usersById.length > 0) {
            users = usersById;
            // 更新用户的 stripe_customer_id
            await this.supabase
              .from('users')
              .update({ stripe_customer_id: customerId })
              .eq('id', customer.metadata.userId);
            console.log('Updated user stripe_customer_id');
          }
        }
      }

      if (!users || users.length === 0) {
        console.error('User not found for customer:', customerId);
        return;
      }

      const user = users[0];
      const planId = fullSubscription.items.data[0].price.id;
      const planDetails = this.planDetails[planId];

      if (!planDetails) {
        console.error('Plan details not found for price:', planId);
        return;
      }

      // 检查是否是取消状态
      const status = fullSubscription.cancel_at_period_end ? 'canceled' : fullSubscription.status;
      
      // 转换时间戳为 ISO 字符串
      const cancelAt = fullSubscription.cancel_at 
        ? new Date(fullSubscription.cancel_at * 1000).toISOString()
        : null;

      // 检查该用户是否已经有这个订阅的积分记录
      const { data: existingTransactions } = await this.supabase
        .from('credit_transactions')
        .select('*')
        .eq('user_id', user.id)
        .eq('type', 'subscription')
        .eq('reference_id', subscriptionId);

      // 只在订阅创建时更新积分，而不是在每次付款时
      const shouldUpdateCredits = 
        eventType === 'customer.subscription.created' && 
        fullSubscription.status === 'active' && 
        (!existingTransactions || existingTransactions.length === 0);

      console.log('Should update credits:', shouldUpdateCredits, 'Event type:', eventType);

      if (shouldUpdateCredits) {
        console.log('Updating credits for user:', user.id, 'Current credits:', user.credits, 'Adding credits:', planDetails.credits);
      }

      // 更新用户的订阅信息
      const { error } = await this.supabase
        .from('users')
        .update({
          subscription_id: subscriptionId,
          subscription_status: status,
          subscription_plan: planDetails.name,
          credits: shouldUpdateCredits ? (user.credits || 0) + planDetails.credits : undefined,
          cancel_at: cancelAt,
          cancel_at_period_end: fullSubscription.cancel_at_period_end
        })
        .eq('id', user.id);

      if (error) {
        console.error('Error updating user in handleSubscriptionUpdate:', error);
        return;
      }

      // 在订阅创建时添加积分交易记录
      if (shouldUpdateCredits) {
        const { error: creditError } = await this.supabase
          .from('credit_transactions')
          .insert({
            user_id: user.id,
            amount: planDetails.credits,
            type: 'subscription',
            reference_id: subscriptionId,
            description: `Credits from ${planDetails.name} subscription (new subscription)`
          });

        if (creditError) {
          console.error('Error creating credit transaction:', creditError);
        } else {
          console.log('Successfully created credit transaction for subscription payment');
        }
      }

      console.log('Successfully updated user subscription');

    } catch (error) {
      console.error('Error handling subscription update:', error);
    }
  }

  private async handleSubscriptionDeleted(subscription: any) {
    try {
      const customerId = subscription.customer;
      const { data: users } = await this.supabase
        .from('users')
        .select('*')
        .eq('stripe_customer_id', customerId)
        .limit(1);

      if (!users || users.length === 0) {
        console.error('User not found for customer:', customerId);
        return;
      }

      const user = users[0];

      // 更新用户的订阅信息
      await this.supabase
        .from('users')
        .update({
          subscription_status: 'canceled',
          subscription_plan: 'free',
          credits: 0
        })
        .eq('id', user.id);

    } catch (error) {
      console.error('Error handling subscription deletion:', error);
    }
  }

  // 获取订阅详情
  async getSubscriptionDetails(subscriptionId: string) {
    try {
      const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);
      return {
        success: true,
        subscription
      };
    } catch (error) {
      console.error('Error retrieving subscription:', error);
      return {
        success: false,
        error: 'Failed to retrieve subscription details'
      };
    }
  }

  // 取消订阅
  async cancelSubscription(subscriptionId: string) {
    try {
      // 获取当前订阅信息
      const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);
      
      // 更新订阅为取消状态
      const updatedSubscription = await this.stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: true
      });

      // 更新用户的订阅状态
      const { data: users } = await this.supabase
        .from('users')
        .select('*')
        .eq('subscription_id', subscriptionId)
        .limit(1);

      if (users && users.length > 0) {
        const user = users[0];
        // 转换时间戳为 ISO 字符串
        const cancelAt = updatedSubscription.cancel_at 
          ? new Date(updatedSubscription.cancel_at * 1000).toISOString()
          : null;

        await this.supabase
          .from('users')
          .update({
            subscription_status: 'canceled',
            cancel_at: cancelAt,
            cancel_at_period_end: true
          })
          .eq('id', user.id);

        console.log('Updated user subscription status to canceled');
      }

      return {
        success: true,
        cancelAt: updatedSubscription.cancel_at
      };
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      return {
        success: false,
        error: 'Failed to cancel subscription'
      };
    }
  }
} 