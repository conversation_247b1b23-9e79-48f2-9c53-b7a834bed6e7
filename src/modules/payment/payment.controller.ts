import { Controller, Post, Body, UseGuards, Req, Get, Query, RawBodyRequest, Headers } from '@nestjs/common';
import { PaymentService } from './payment.service';
import { AuthGuard } from '@nestjs/passport';
import { Request } from 'express';
import { ConfigService } from '@nestjs/config';

interface RequestWithUser extends Request {
  user: {
    id: string;
    [key: string]: any;
  };
}

@Controller('payment')
export class PaymentController {
  constructor(
    private readonly paymentService: PaymentService,
    private readonly configService: ConfigService
  ) {}

  @Post('create-checkout-session')
  @UseGuards(AuthGuard('jwt'))
  async createCheckoutSession(
    @Body() body: { priceId: string },
    @Req() req: RequestWithUser
  ): Promise<{ url: string; sessionId: string }> {
    const userId = req.user.id;
    return this.paymentService.createCheckoutSession(body.priceId, userId);
  }

  @Get('check-session')
  async checkSession(
    @Query('sessionId') sessionId: string
  ) {
    return this.paymentService.checkSessionStatus(sessionId);
  }

  @Post('subscription-details')
  @UseGuards(AuthGuard('jwt'))
  async getSubscriptionDetails(
    @Body() body: { subscriptionId: string }
  ) {
    return this.paymentService.getSubscriptionDetails(body.subscriptionId);
  }

  @Post('cancel-subscription')
  @UseGuards(AuthGuard('jwt'))
  async cancelSubscription(
    @Body() body: { subscriptionId: string }
  ) {
    return this.paymentService.cancelSubscription(body.subscriptionId);
  }

  @Post('webhook')
  async handleWebhook(
    @Body() rawBody: Buffer,
    @Headers('stripe-signature') signature: string,
  ) {
    return this.paymentService.handleWebhook(signature, rawBody);
  }
} 