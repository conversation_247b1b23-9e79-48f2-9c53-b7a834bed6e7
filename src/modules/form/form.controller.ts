import { Controller, Post, Body, HttpStatus } from '@nestjs/common';
import { FormService } from './services/form.service';
import { FormRequest, FormResponse } from '../../common/types/form.types';
import { AppError } from '../../common/utils/error';
import { Inject } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';

@Controller('v1/form')
export class FormController {
  constructor(
    private readonly formService: FormService,
    @Inject('SUPABASE')
    private supabase: SupabaseClient
  ) {}

  @Post('generate')
  async generateForm(@Body() body: FormRequest): Promise<FormResponse> {
    try {
      // 验证请求
      if (!body.description) {
        throw new AppError(HttpStatus.BAD_REQUEST, 'Description is required');
      }
      if (!body.formFields || !Array.isArray(body.formFields)) {
        throw new AppError(HttpStatus.BAD_REQUEST, 'Form fields are required and must be an array');
      }
      if (!body.mode || !['bugReport', 'general', 'email'].includes(body.mode)) {
        throw new AppError(HttpStatus.BAD_REQUEST, 'Invalid mode specified');
      }
      
      // 当不使用自定义API时，验证用户身份和credits
      if (!body.useCustomApi) {
        if (!body.userId) {
          throw new AppError(HttpStatus.UNAUTHORIZED, 'User authentication required');
        }

        // 验证用户是否存在且有足够的credits
        const { data: user, error } = await this.supabase
          .from('users')
          .select('credits')
          .eq('id', body.userId)
          .single();

        if (error || !user) {
          throw new AppError(HttpStatus.UNAUTHORIZED, 'Invalid user');
        }

        // 预估本次请求所需的最小积分（保守估计为 1 credit）
        const minimumRequiredCredits = 1;

        if (!user.credits || user.credits < minimumRequiredCredits) {
          throw new AppError(HttpStatus.PAYMENT_REQUIRED, 'Insufficient credits');
        }
      } else {
        // 使用自定义API时，验证API key
        if (!body.apiKey) {
          throw new AppError(HttpStatus.BAD_REQUEST, 'API key is required when using custom API');
        }
      }

      // 项目信息现在直接包含在 body.project 中，不需要额外处理

      const result = await this.formService.generateFormContent(body);

      return {
        success: true,
        data: result
      };
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(
        HttpStatus.INTERNAL_SERVER_ERROR,
        error instanceof Error ? error.message : 'Internal server error'
      );
    }
  }
} 