import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { FormController } from './form.controller';
import { FormService } from './services/form.service';
import { createClient } from '@supabase/supabase-js';

@Module({
  imports: [ConfigModule],
  controllers: [FormController],
  providers: [
    FormService,
    {
      provide: 'SUPABASE',
      useFactory: () => {
        const supabaseUrl = process.env.SUPABASE_URL;
        const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
        if (!supabaseUrl || !supabaseKey) {
          throw new Error('Supabase configuration is missing');
        }
        return createClient(supabaseUrl, supabaseKey);
      }
    }
  ],
})
export class FormModule {} 